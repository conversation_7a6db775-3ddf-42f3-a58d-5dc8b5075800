#!/usr/bin/env python3
"""
🔧 QUICK FIX - INSTANCE_ID NULL CONSTRAINT
Modification rapide pour permettre NULL dans instance_id
"""

import psycopg2
import logging
from datetime import datetime

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'quick_fix_instance_id_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def connect_dw():
    """Connexion au Data Warehouse"""
    try:
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            database='aya',
            user='jirauser',
            password='mypassword'
        )
        conn.autocommit = False
        return conn
    except Exception as e:
        logger.error(f"Erreur connexion DW: {e}")
        raise

def fix_instance_id_constraint():
    """Modifier la contrainte instance_id pour permettre NULL"""
    logger.info("MODIFICATION CONTRAINTE INSTANCE_ID")
    logger.info("=" * 50)
    
    conn = connect_dw()
    cursor = conn.cursor()
    
    try:
        # Modifier la contrainte pour permettre NULL
        cursor.execute("""
            ALTER TABLE load.fact_jiraissue 
            ALTER COLUMN instance_id DROP NOT NULL
        """)
        
        conn.commit()
        logger.info("✅ Contrainte instance_id modifiée (NULL autorisé)")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur modification contrainte: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def insert_fact_jiraissue():
    """Insérer fact_jiraissue avec instance_id NULL autorisé"""
    logger.info("\nINSERTION FACT_JIRAISSUE")
    logger.info("-" * 50)
    
    conn = connect_dw()
    cursor = conn.cursor()
    
    try:
        # Vider fact_jiraissue d'abord
        cursor.execute("TRUNCATE TABLE load.fact_jiraissue")
        logger.info("✅ fact_jiraissue vidée")
        
        # Obtenir les colonnes communes (TOUTES les colonnes jiraissue)
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_schema = 'transform' AND table_name = 'jiraissue_clean'
            AND column_name IN (
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = 'load' AND table_name = 'fact_jiraissue'
                AND column_name NOT LIKE '%_id' OR column_name = 'instance_id'
            )
            ORDER BY ordinal_position
        """)
        
        jiraissue_columns = [row[0] for row in cursor.fetchall()]
        
        if not jiraissue_columns:
            logger.error("❌ Aucune colonne commune trouvée")
            return 0
        
        # Insérer les données avec instance_id NULL autorisé
        columns_list = ', '.join([f'"{col}"' for col in jiraissue_columns])
        
        insert_sql = f'''
            INSERT INTO load.fact_jiraissue ({columns_list})
            SELECT {columns_list}
            FROM transform.jiraissue_clean
        '''
        
        logger.info(f"🚀 Insertion de {len(jiraissue_columns)} colonnes...")
        logger.info(f"📋 Colonnes: {jiraissue_columns[:5]}...")
        
        cursor.execute(insert_sql)
        records_inserted = cursor.rowcount
        
        conn.commit()
        
        logger.info(f"✅ {records_inserted:,} records fact_jiraissue insérés")
        return records_inserted
        
    except Exception as e:
        logger.error(f"❌ Erreur insertion: {e}")
        conn.rollback()
        return 0
    finally:
        conn.close()

def validate_result():
    """Valider le résultat final"""
    logger.info("\nVALIDATION FINALE")
    logger.info("-" * 50)
    
    conn = connect_dw()
    cursor = conn.cursor()
    
    try:
        # Compter fact_jiraissue
        cursor.execute('SELECT COUNT(*) FROM load.fact_jiraissue')
        fact_count = cursor.fetchone()[0]
        
        # Compter transform.jiraissue_clean
        cursor.execute('SELECT COUNT(*) FROM transform.jiraissue_clean')
        transform_count = cursor.fetchone()[0]
        
        logger.info(f"📊 Transform jiraissue: {transform_count:,} records")
        logger.info(f"📊 Fact jiraissue: {fact_count:,} records")
        
        success = fact_count == transform_count and fact_count > 0
        
        if success:
            logger.info(f"\n✅ SUCCÈS COMPLET - FACT_JIRAISSUE OPÉRATIONNEL!")
            logger.info(f"🎯 {fact_count:,} records insérés avec instance_id NULL autorisé")
        else:
            logger.info(f"\n⚠️ PROBLÈME PERSISTANT")
            logger.info(f"❌ Expected: {transform_count:,}, Got: {fact_count:,}")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Erreur validation: {e}")
        return False
    finally:
        conn.close()

def main():
    """Point d'entrée principal"""
    print("🔧 QUICK FIX - INSTANCE_ID NULL CONSTRAINT")
    print("=" * 60)
    print("🔧 Modification contrainte instance_id")
    print("📊 Insertion fact_jiraissue")
    print("✅ Validation finale")
    print("=" * 60)
    
    try:
        # Étape 1: Modifier la contrainte
        if not fix_instance_id_constraint():
            logger.error("❌ Échec modification contrainte")
            return 1
        
        # Étape 2: Insérer les données
        records_inserted = insert_fact_jiraissue()
        
        if records_inserted == 0:
            logger.error("❌ Échec insertion fact_jiraissue")
            return 1
        
        # Étape 3: Valider le résultat
        success = validate_result()
        return 0 if success else 1
        
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
