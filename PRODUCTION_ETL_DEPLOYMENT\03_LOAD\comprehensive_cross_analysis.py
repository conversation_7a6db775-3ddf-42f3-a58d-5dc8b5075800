#!/usr/bin/env python3
"""
🔍 COMPREHENSIVE CROSS-ANALYSIS: TRANSFORM vs LOAD
Analyse complète des scripts ETL pour identifier TOUS les problèmes

ANALYSE COMPLÈTE:
1. ✅ Structure des tables (colonnes, types, contraintes)
2. ✅ Insertion des données (mappings, transformations)
3. ✅ Validation croisée (counts, intégrité)
4. ✅ Identification des problèmes spécifiques
5. ✅ Recommandations de correction

OBJECTIF: Identifier EXACTEMENT ce qui doit être corrigé
"""

import psycopg2
import logging
from datetime import datetime

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'comprehensive_cross_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComprehensiveCrossAnalyzer:
    """
    Analyseur complet pour identifier TOUS les problèmes ETL
    """
    
    def __init__(self):
        # Configuration Data Warehouse
        self.dw_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        # 52 tables critiques
        self.all_tables = [
            # CORE_BUSINESS (12 tables)
            'jiraissue', 'project', 'component', 'projectversion',
            'customfield', 'customfieldvalue', 'worklog', 'fileattachment',
            'issuelink', 'issuelinktype', 'label', 'nodeassociation',
            
            # USERS_GROUPS (8 tables)
            'cwd_user', 'cwd_group', 'cwd_membership', 'cwd_user_attributes',
            'userassociation', 'projectroleactor', 'app_user', 'cwd_directory',
            
            # WORKFLOWS (5 tables)
            'jiraworkflows', 'workflowscheme', 'workflowschemeentity',
            'os_currentstep', 'os_wfentry',
            
            # CONFIGURATION (6 tables)
            'fieldconfiguration', 'fieldconfigscheme', 'permissionscheme',
            'schemepermissions', 'fieldscreen', 'fieldscreentab',
            
            # CHANGES_HISTORY (3 tables)
            'changegroup', 'changeitem', 'jiraaction',
            
            # PLUGINS_MANAGEMENT (2 tables)
            'pluginversion', 'managedconfigurationitem',
            
            # LOOKUP_TABLES (6 tables)
            'issuestatus', 'priority', 'resolution', 'issuetype',
            'projectrole', 'projectcategory',
            
            # SCRIPT_RUNNER (4 tables)
            'AO_4B00E6_SR_USER_PROP', 'AO_4B00E6_STASH_SETTINGS',
            'AO_4B00E6_UPGRADE_BACKUP', 'AO_786AC3_SQL_FAVOURITE',
            
            # AGILE_BOARDS (3 tables)
            'AO_60DB71_RAPIDVIEW', 'AO_60DB71_SPRINT', 'AO_60DB71_ISSUERANKING',
            
            # JSM_AUDIT (3 tables)
            'AO_C77861_AUDIT_ENTITY', 'AO_C77861_AUDIT_ACTION_CACHE',
            'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]
        
        # Statistiques d'analyse
        self.analysis_stats = {
            'start_time': datetime.now(),
            'tables_analyzed': 0,
            'structure_issues': [],
            'data_issues': [],
            'mapping_issues': [],
            'critical_errors': [],
            'recommendations': []
        }
    
    def connect_dw(self):
        """Connexion au Data Warehouse"""
        try:
            conn = psycopg2.connect(**self.dw_config)
            conn.autocommit = True
            return conn
        except Exception as e:
            logger.error(f"❌ Erreur connexion DW: {e}")
            raise
    
    def analyze_table_structure_differences(self, table_name):
        """Analyser les différences de structure entre transform et load"""
        logger.info(f"🔍 Analyse structure: {table_name}")
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            transform_table = f"{table_name}_clean"
            
            # Structure TRANSFORM
            cursor.execute("""
                SELECT column_name, data_type, character_maximum_length, 
                       numeric_precision, numeric_scale, is_nullable
                FROM information_schema.columns
                WHERE table_schema = 'transform' AND table_name = %s
                ORDER BY ordinal_position
            """, (transform_table,))
            
            transform_columns = {row[0]: row[1:] for row in cursor.fetchall()}
            
            # Structure LOAD dimension
            dim_table = f"dim_{table_name}"
            cursor.execute("""
                SELECT column_name, data_type, character_maximum_length,
                       numeric_precision, numeric_scale, is_nullable
                FROM information_schema.columns
                WHERE table_schema = 'load' AND table_name = %s
                ORDER BY ordinal_position
            """, (dim_table,))
            
            load_columns = {row[0]: row[1:] for row in cursor.fetchall()}
            
            # Analyser les différences
            structure_analysis = {
                'table_name': table_name,
                'transform_exists': len(transform_columns) > 0,
                'load_exists': len(load_columns) > 0,
                'transform_columns': len(transform_columns),
                'load_columns': len(load_columns),
                'missing_in_load': [],
                'missing_in_transform': [],
                'type_mismatches': [],
                'common_columns': 0
            }
            
            # Colonnes manquantes dans load
            for col in transform_columns:
                if col not in load_columns:
                    structure_analysis['missing_in_load'].append(col)
            
            # Colonnes manquantes dans transform
            for col in load_columns:
                if col not in transform_columns:
                    structure_analysis['missing_in_transform'].append(col)
            
            # Types incompatibles
            for col in transform_columns:
                if col in load_columns:
                    structure_analysis['common_columns'] += 1
                    if transform_columns[col] != load_columns[col]:
                        structure_analysis['type_mismatches'].append({
                            'column': col,
                            'transform_type': transform_columns[col],
                            'load_type': load_columns[col]
                        })
            
            return structure_analysis
            
        except Exception as e:
            logger.error(f"❌ Erreur analyse structure {table_name}: {e}")
            return None
        finally:
            conn.close()
    
    def analyze_data_insertion_issues(self, table_name):
        """Analyser les problèmes d'insertion de données"""
        logger.info(f"📊 Analyse données: {table_name}")
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            transform_table = f"{table_name}_clean"
            dim_table = f"dim_{table_name}"
            
            # Compter les enregistrements
            cursor.execute(f'SELECT COUNT(*) FROM transform."{transform_table}"')
            transform_count = cursor.fetchone()[0] if cursor.rowcount > 0 else 0
            
            cursor.execute(f'SELECT COUNT(*) FROM load."{dim_table}"')
            load_count = cursor.fetchone()[0] if cursor.rowcount > 0 else 0
            
            # Analyser les données
            data_analysis = {
                'table_name': table_name,
                'transform_count': transform_count,
                'load_count': load_count,
                'data_loss': transform_count - load_count,
                'insertion_success': load_count > 0,
                'complete_transfer': transform_count == load_count,
                'sample_data_issues': []
            }
            
            # Vérifier les échantillons de données si les tables existent
            if transform_count > 0 and load_count > 0:
                # Échantillon transform
                cursor.execute(f'SELECT * FROM transform."{transform_table}" LIMIT 1')
                transform_sample = cursor.fetchone()
                
                # Échantillon load
                cursor.execute(f'SELECT * FROM load."{dim_table}" LIMIT 1')
                load_sample = cursor.fetchone()
                
                if transform_sample and load_sample:
                    if len(transform_sample) != len(load_sample):
                        data_analysis['sample_data_issues'].append(
                            f"Column count mismatch: transform={len(transform_sample)}, load={len(load_sample)}"
                        )
            
            return data_analysis
            
        except Exception as e:
            logger.error(f"❌ Erreur analyse données {table_name}: {e}")
            return None
        finally:
            conn.close()
    
    def analyze_fact_table_issues(self):
        """Analyser spécifiquement les problèmes de fact_jiraissue"""
        logger.info("📊 Analyse FACT_JIRAISSUE")
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            # Vérifier l'existence des tables
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables
                WHERE table_schema = 'transform' AND table_name = 'jiraissue_clean'
            """)
            transform_exists = cursor.fetchone()[0] > 0
            
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables
                WHERE table_schema = 'load' AND table_name = 'fact_jiraissue'
            """)
            fact_exists = cursor.fetchone()[0] > 0
            
            fact_analysis = {
                'transform_jiraissue_exists': transform_exists,
                'fact_jiraissue_exists': fact_exists,
                'transform_count': 0,
                'fact_count': 0,
                'column_mapping_issues': [],
                'constraint_issues': []
            }
            
            if transform_exists:
                cursor.execute('SELECT COUNT(*) FROM transform.jiraissue_clean')
                fact_analysis['transform_count'] = cursor.fetchone()[0]
            
            if fact_exists:
                cursor.execute('SELECT COUNT(*) FROM load.fact_jiraissue')
                fact_analysis['fact_count'] = cursor.fetchone()[0]
                
                # Analyser les colonnes communes
                cursor.execute("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_schema = 'transform' AND table_name = 'jiraissue_clean'
                    AND column_name IN (
                        SELECT column_name
                        FROM information_schema.columns
                        WHERE table_schema = 'load' AND table_name = 'fact_jiraissue'
                        AND column_name NOT LIKE '%_id'
                    )
                """)
                
                common_columns = [row[0] for row in cursor.fetchall()]
                fact_analysis['common_columns_count'] = len(common_columns)
                
                if len(common_columns) == 0:
                    fact_analysis['column_mapping_issues'].append("No common columns found for data insertion")
            
            return fact_analysis

        except Exception as e:
            logger.error(f"❌ Erreur analyse fact table: {e}")
            return None
        finally:
            conn.close()

    def run_comprehensive_analysis(self):
        """Exécuter l'analyse complète de tous les problèmes"""
        logger.info("🚀 DÉMARRAGE ANALYSE COMPLÈTE TRANSFORM vs LOAD")
        logger.info("=" * 80)

        # Analyser toutes les tables
        for i, table_name in enumerate(self.all_tables, 1):
            logger.info(f"\n[{i:2d}/52] ANALYSE: {table_name}")

            # Analyse structure
            structure_analysis = self.analyze_table_structure_differences(table_name)
            if structure_analysis:
                if structure_analysis['missing_in_load'] or structure_analysis['type_mismatches']:
                    self.analysis_stats['structure_issues'].append(structure_analysis)

            # Analyse données
            data_analysis = self.analyze_data_insertion_issues(table_name)
            if data_analysis:
                if not data_analysis['complete_transfer'] or not data_analysis['insertion_success']:
                    self.analysis_stats['data_issues'].append(data_analysis)

            self.analysis_stats['tables_analyzed'] += 1

        # Analyse spécifique fact table
        fact_analysis = self.analyze_fact_table_issues()
        if fact_analysis and fact_analysis['fact_count'] == 0:
            self.analysis_stats['critical_errors'].append(fact_analysis)

        # Générer le rapport final
        self.generate_comprehensive_report()

    def generate_comprehensive_report(self):
        """Générer le rapport complet avec recommandations"""
        logger.info("\n📋 RAPPORT COMPLET D'ANALYSE")
        logger.info("=" * 80)

        duration = (datetime.now() - self.analysis_stats['start_time']).total_seconds()

        logger.info(f"⏱️ Durée analyse: {duration:.2f} secondes")
        logger.info(f"📊 Tables analysées: {self.analysis_stats['tables_analyzed']}/52")
        logger.info(f"🏗️ Problèmes structure: {len(self.analysis_stats['structure_issues'])}")
        logger.info(f"📊 Problèmes données: {len(self.analysis_stats['data_issues'])}")
        logger.info(f"🚨 Erreurs critiques: {len(self.analysis_stats['critical_errors'])}")

        # PROBLÈMES DE STRUCTURE
        if self.analysis_stats['structure_issues']:
            logger.info(f"\n🏗️ PROBLÈMES DE STRUCTURE ({len(self.analysis_stats['structure_issues'])} tables)")
            logger.info("-" * 60)

            for issue in self.analysis_stats['structure_issues'][:10]:
                logger.info(f"❌ {issue['table_name']}:")
                if issue['missing_in_load']:
                    logger.info(f"   Colonnes manquantes dans load: {len(issue['missing_in_load'])}")
                if issue['type_mismatches']:
                    logger.info(f"   Types incompatibles: {len(issue['type_mismatches'])}")

        # PROBLÈMES DE DONNÉES
        if self.analysis_stats['data_issues']:
            logger.info(f"\n📊 PROBLÈMES DE DONNÉES ({len(self.analysis_stats['data_issues'])} tables)")
            logger.info("-" * 60)

            for issue in self.analysis_stats['data_issues'][:10]:
                logger.info(f"❌ {issue['table_name']}:")
                logger.info(f"   Transform: {issue['transform_count']:,} records")
                logger.info(f"   Load: {issue['load_count']:,} records")
                logger.info(f"   Perte: {issue['data_loss']:,} records")

        # ERREURS CRITIQUES
        if self.analysis_stats['critical_errors']:
            logger.info(f"\n🚨 ERREURS CRITIQUES")
            logger.info("-" * 60)

            for error in self.analysis_stats['critical_errors']:
                logger.info(f"❌ FACT_JIRAISSUE:")
                logger.info(f"   Transform records: {error['transform_count']:,}")
                logger.info(f"   Fact records: {error['fact_count']:,}")
                logger.info(f"   Colonnes communes: {error.get('common_columns_count', 0)}")

        # RECOMMANDATIONS
        self.generate_recommendations()

        # STATUT FINAL
        total_issues = (len(self.analysis_stats['structure_issues']) +
                       len(self.analysis_stats['data_issues']) +
                       len(self.analysis_stats['critical_errors']))

        if total_issues == 0:
            logger.info(f"\n✅ ANALYSE RÉUSSIE - AUCUN PROBLÈME DÉTECTÉ!")
            return True
        else:
            logger.info(f"\n⚠️ PROBLÈMES DÉTECTÉS - {total_issues} CORRECTIONS NÉCESSAIRES")
            return False

    def generate_recommendations(self):
        """Générer les recommandations de correction"""
        logger.info(f"\n💡 RECOMMANDATIONS DE CORRECTION")
        logger.info("-" * 60)

        recommendations = []

        # Recommandations structure
        if self.analysis_stats['structure_issues']:
            recommendations.append("1. CORRIGER DDL LOAD: Vérifier les types de colonnes dans 01_load_ddl.py")
            recommendations.append("2. SYNCHRONISER STRUCTURES: Assurer correspondance exacte transform → load")

        # Recommandations données
        if self.analysis_stats['data_issues']:
            recommendations.append("3. CORRIGER INSERT LOAD: Vérifier les mappings dans 02_load_insert.py")
            recommendations.append("4. VALIDER CONTRAINTES: Vérifier les contraintes NOT NULL")

        # Recommandations critiques
        if self.analysis_stats['critical_errors']:
            recommendations.append("5. CORRIGER FACT_JIRAISSUE: Problème critique d'insertion")
            recommendations.append("6. VÉRIFIER COLONNES COMMUNES: Mapping transform → fact")

        # Recommandations générales
        recommendations.extend([
            "7. EXÉCUTER VALIDATION: Lancer 03_load_validation.py après corrections",
            "8. TESTER PIPELINE: Validation end-to-end complète"
        ])

        for i, rec in enumerate(recommendations, 1):
            logger.info(f"   {rec}")

        self.analysis_stats['recommendations'] = recommendations

def main():
    """Point d'entrée principal"""
    print("🔍 ANALYSE COMPLÈTE TRANSFORM vs LOAD")
    print("=" * 60)
    print("📋 Analyse structure des tables")
    print("📊 Analyse insertion des données")
    print("🚨 Identification erreurs critiques")
    print("💡 Génération recommandations")
    print("=" * 60)

    analyzer = ComprehensiveCrossAnalyzer()

    try:
        analyzer.run_comprehensive_analysis()
        return 0
    except Exception as e:
        logger.error(f"❌ Erreur fatale analyse: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
