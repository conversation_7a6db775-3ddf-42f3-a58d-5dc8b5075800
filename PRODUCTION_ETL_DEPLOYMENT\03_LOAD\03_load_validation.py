#!/usr/bin/env python3
"""
🔍 VALIDATION SCHÉMA LOAD - FIXED VERSION
Validation complète du schéma load avec corrections pour star schema

VALIDATIONS:
1. ✅ Validation des 51 tables dimension
2. ✅ Validation de la table fact_jiraissue
3. ✅ Validation des clés étrangères
4. ✅ Validation de l'intégrité des données
5. ✅ Validation des counts et cohérence

ARCHITECTURE CORRECTE:
- 51 dimensions = dim_* (copies exactes de transform)
- 1 fact table = fact_jiraissue (jiraissue data + 51 FK)
- FK relationships entre fact et dimensions
"""

import psycopg2
import logging
from datetime import datetime
import time

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'load_validation_fixed_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LoadValidator:
    """
    Validation du schéma load - FIXED VERSION
    """

    def __init__(self):
        # Configuration Data Warehouse
        self.dw_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }

        # 51 tables dimension à valider
        self.dimension_tables = [
            # CORE_BUSINESS (11 tables - sans jiraissue)
            'project', 'component', 'projectversion',
            'customfield', 'customfieldvalue', 'worklog', 'fileattachment',
            'issuelink', 'issuelinktype', 'label', 'nodeassociation',

            # USERS_GROUPS (8 tables)
            'cwd_user', 'cwd_group', 'cwd_membership', 'cwd_user_attributes',
            'userassociation', 'projectroleactor', 'app_user', 'cwd_directory',

            # WORKFLOWS (5 tables)
            'jiraworkflows', 'workflowscheme', 'workflowschemeentity',
            'os_currentstep', 'os_wfentry',

            # CONFIGURATION (6 tables)
            'fieldconfiguration', 'fieldconfigscheme', 'permissionscheme',
            'schemepermissions', 'fieldscreen', 'fieldscreentab',

            # CHANGES_HISTORY (3 tables)
            'changegroup', 'changeitem', 'jiraaction',

            # PLUGINS_MANAGEMENT (2 tables)
            'pluginversion', 'managedconfigurationitem',

            # LOOKUP_TABLES (6 tables)
            'issuestatus', 'priority', 'resolution', 'issuetype',
            'projectrole', 'projectcategory',

            # SCRIPT_RUNNER (4 tables)
            'AO_4B00E6_SR_USER_PROP', 'AO_4B00E6_STASH_SETTINGS',
            'AO_4B00E6_UPGRADE_BACKUP', 'AO_786AC3_SQL_FAVOURITE',

            # AGILE_BOARDS (3 tables)
            'AO_60DB71_RAPIDVIEW', 'AO_60DB71_SPRINT', 'AO_60DB71_ISSUERANKING',

            # JSM_AUDIT (3 tables)
            'AO_C77861_AUDIT_ENTITY', 'AO_C77861_AUDIT_ACTION_CACHE',
            'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]

        # Table fact
        self.fact_table = 'fact_jiraissue'

        self.stats = {
            'start_time': datetime.now(),
            'dimensions_validated': 0,
            'fact_validated': 0,
            'total_records': 0,
            'fk_relationships': 0,
            'validation_errors': [],
            'warnings': []
        }
    
    def connect_dw(self):
        """Connexion au Data Warehouse"""
        try:
            conn = psycopg2.connect(**self.dw_config)
            conn.autocommit = True
            return conn
        except Exception as e:
            logger.error(f"❌ Erreur connexion DW: {e}")
            raise
    
    def validate_schema_structure(self):
        """Valider la structure du schéma load"""
        logger.info("🏗️ VALIDATION STRUCTURE SCHÉMA")
        logger.info("=" * 60)
        
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Vérifier l'existence du schéma
            cursor.execute("""
                SELECT schema_name FROM information_schema.schemata 
                WHERE schema_name = 'load'
            """)
            
            if not cursor.fetchone():
                logger.error("❌ Schéma 'load' non trouvé")
                return False
            
            logger.info("✅ Schéma 'load' existe")
            
            # Compter les tables
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = 'load'
            """)
            
            table_count = cursor.fetchone()[0]
            logger.info(f"📊 Tables dans le schéma load: {table_count}")
            
            # Lister les tables par type
            cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'load'
                ORDER BY table_name
            """)
            
            all_tables = [row[0] for row in cursor.fetchall()]
            
            regular_tables = [t for t in all_tables if not (t.startswith('fact_') or t.startswith('dim_'))]
            fact_dim_tables = [t for t in all_tables if t.startswith('fact_') or t.startswith('dim_')]
            
            logger.info(f"   📋 Tables régulières: {len(regular_tables)}")
            logger.info(f"   🌟 Tables fact/dimension: {len(fact_dim_tables)}")
            
            if len(regular_tables) >= 50:  # Au moins 50/52 tables
                logger.info("✅ Structure schéma validée")
                return True
            else:
                logger.warning(f"⚠️ Seulement {len(regular_tables)} tables régulières trouvées")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erreur validation structure: {e}")
            return False
        finally:
            dw_conn.close()
    
    def validate_table_data(self, table_name):
        """Valider les données d'une table"""
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Compter les enregistrements
            cursor.execute(f'SELECT COUNT(*) FROM load."{table_name}"')
            record_count = cursor.fetchone()[0]
            
            if record_count == 0:
                self.stats['warnings'].append(f"{table_name}: Table vide")
                logger.warning(f"   ⚠️ {table_name}: Table vide")
                return 0
            
            # Vérifier les colonnes NULL critiques
            cursor.execute(f"""
                SELECT column_name FROM information_schema.columns 
                WHERE table_schema = 'load' AND table_name = %s
                AND column_name IN ('id', 'instance_id')
                AND is_nullable = 'NO'
            """, (table_name,))
            
            critical_columns = [row[0] for row in cursor.fetchall()]
            
            for col in critical_columns:
                cursor.execute(f'SELECT COUNT(*) FROM load."{table_name}" WHERE "{col}" IS NULL')
                null_count = cursor.fetchone()[0]
                
                if null_count > 0:
                    self.stats['validation_errors'].append(f"{table_name}.{col}: {null_count} valeurs NULL")
                    logger.error(f"   ❌ {table_name}.{col}: {null_count} valeurs NULL")
            
            logger.info(f"   ✅ {table_name}: {record_count:,} enregistrements")
            self.stats['tables_validated'] += 1
            self.stats['total_records'] += record_count
            
            return record_count
            
        except Exception as e:
            logger.error(f"   ❌ {table_name}: Erreur validation - {e}")
            self.stats['validation_errors'].append(f"{table_name}: {e}")
            return 0
        finally:
            dw_conn.close()
    
    def validate_critical_tables(self):
        """Valider les tables critiques"""
        logger.info(f"\n📋 VALIDATION TABLES CRITIQUES")
        logger.info("=" * 60)
        
        for table in self.critical_tables:
            logger.info(f"🔍 Validation {table}")
            self.validate_table_data(table)
    
    def validate_fact_dimension_tables(self):
        """Valider les tables fact et dimension"""
        logger.info(f"\n🌟 VALIDATION TABLES FACT/DIMENSION")
        logger.info("=" * 60)
        
        for table in self.fact_dim_tables:
            logger.info(f"🔍 Validation {table}")
            self.validate_table_data(table)
    
    def validate_foreign_keys(self):
        """Valider les clés étrangères"""
        logger.info(f"\n🔗 VALIDATION CLÉS ÉTRANGÈRES")
        logger.info("=" * 60)
        
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Lister toutes les FK dans le schéma load
            cursor.execute("""
                SELECT 
                    tc.constraint_name,
                    tc.table_name,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                    ON ccu.constraint_name = tc.constraint_name
                    AND ccu.table_schema = tc.table_schema
                WHERE tc.constraint_type = 'FOREIGN KEY'
                AND tc.table_schema = 'load'
                ORDER BY tc.table_name, tc.constraint_name
            """)
            
            foreign_keys = cursor.fetchall()
            
            logger.info(f"📊 Clés étrangères trouvées: {len(foreign_keys)}")
            
            for constraint_name, table_name, column_name, foreign_table, foreign_column in foreign_keys:
                logger.info(f"   ✅ {table_name}.{column_name} → {foreign_table}.{foreign_column}")
                
                # Vérifier l'intégrité référentielle
                cursor.execute(f"""
                    SELECT COUNT(*) FROM load."{table_name}" t
                    WHERE t."{column_name}" IS NOT NULL
                    AND NOT EXISTS (
                        SELECT 1 FROM load."{foreign_table}" f
                        WHERE f."{foreign_column}" = t."{column_name}"
                    )
                """)
                
                orphan_count = cursor.fetchone()[0]
                
                if orphan_count > 0:
                    self.stats['validation_errors'].append(
                        f"FK {constraint_name}: {orphan_count} enregistrements orphelins"
                    )
                    logger.error(f"   ❌ FK {constraint_name}: {orphan_count} enregistrements orphelins")
                else:
                    logger.info(f"      ✅ Intégrité référentielle OK")
            
            self.stats['fk_relationships'] = len(foreign_keys)
            
            if len(foreign_keys) >= 8:  # Au moins 8 FK attendues
                logger.info("✅ Validation clés étrangères réussie")
                return True
            else:
                logger.warning(f"⚠️ Seulement {len(foreign_keys)} clés étrangères trouvées")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erreur validation FK: {e}")
            return False
        finally:
            dw_conn.close()
    
    def validate_data_consistency(self):
        """Valider la cohérence des données"""
        logger.info(f"\n🔍 VALIDATION COHÉRENCE DONNÉES")
        logger.info("=" * 60)
        
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Vérifier la cohérence fact_issues vs jiraissue_clean
            cursor.execute('SELECT COUNT(*) FROM load.fact_issues')
            fact_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM load.jiraissue_clean')
            jira_count = cursor.fetchone()[0]
            
            logger.info(f"📊 fact_issues: {fact_count:,} enregistrements")
            logger.info(f"📊 jiraissue_clean: {jira_count:,} enregistrements")
            
            if fact_count > 0 and jira_count > 0:
                ratio = fact_count / jira_count
                if 0.8 <= ratio <= 1.2:  # Tolérance de 20%
                    logger.info(f"✅ Cohérence fact_issues/jiraissue_clean OK ({ratio:.2%})")
                else:
                    self.stats['warnings'].append(f"Ratio fact_issues/jiraissue_clean: {ratio:.2%}")
                    logger.warning(f"⚠️ Ratio fact_issues/jiraissue_clean: {ratio:.2%}")
            
            # Vérifier la cohérence dim_users vs cwd_user_clean
            cursor.execute('SELECT COUNT(*) FROM load.dim_users')
            dim_users_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM load.cwd_user_clean')
            cwd_users_count = cursor.fetchone()[0]
            
            logger.info(f"📊 dim_users: {dim_users_count:,} enregistrements")
            logger.info(f"📊 cwd_user_clean: {cwd_users_count:,} enregistrements")
            
            if dim_users_count > 0 and cwd_users_count > 0:
                ratio = dim_users_count / cwd_users_count
                if 0.8 <= ratio <= 1.2:  # Tolérance de 20%
                    logger.info(f"✅ Cohérence dim_users/cwd_user_clean OK ({ratio:.2%})")
                else:
                    self.stats['warnings'].append(f"Ratio dim_users/cwd_user_clean: {ratio:.2%}")
                    logger.warning(f"⚠️ Ratio dim_users/cwd_user_clean: {ratio:.2%}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur validation cohérence: {e}")
            return False
        finally:
            dw_conn.close()
    
    def generate_validation_report(self):
        """Générer le rapport de validation"""
        logger.info(f"\n📋 RAPPORT DE VALIDATION")
        logger.info("=" * 60)
        
        duration = (datetime.now() - self.stats['start_time']).total_seconds()
        
        logger.info(f"⏱️ Durée validation: {duration:.2f} secondes")
        logger.info(f"📊 Tables validées: {self.stats['tables_validated']}")
        logger.info(f"📈 Total enregistrements: {self.stats['total_records']:,}")
        logger.info(f"🔗 Clés étrangères: {self.stats['fk_relationships']}")
        logger.info(f"❌ Erreurs: {len(self.stats['validation_errors'])}")
        logger.info(f"⚠️ Avertissements: {len(self.stats['warnings'])}")
        
        if self.stats['validation_errors']:
            logger.error(f"\n🚨 ERREURS DE VALIDATION:")
            for error in self.stats['validation_errors'][:10]:
                logger.error(f"   - {error}")
        
        if self.stats['warnings']:
            logger.warning(f"\n⚠️ AVERTISSEMENTS:")
            for warning in self.stats['warnings'][:10]:
                logger.warning(f"   - {warning}")
        
        # Déterminer le statut global
        if len(self.stats['validation_errors']) == 0 and self.stats['total_records'] > 50000:
            logger.info(f"\n🎯 VALIDATION RÉUSSIE!")
            logger.info(f"   ✅ Schéma load validé avec succès")
            logger.info(f"   ✅ {self.stats['total_records']:,} enregistrements validés")
            logger.info(f"   ✅ {self.stats['fk_relationships']} clés étrangères validées")
            logger.info(f"   ✅ Prêt pour la production")
            return True
        else:
            logger.warning(f"\n⚠️ VALIDATION PARTIELLE")
            logger.warning(f"   Erreurs: {len(self.stats['validation_errors'])}")
            logger.warning(f"   Enregistrements: {self.stats['total_records']:,}")
            return False

def main():
    """Point d'entrée principal"""
    print("🔍 VALIDATION SCHÉMA LOAD - NEW APPROACH")
    print("=" * 60)
    print("📋 Validation des 52 tables copiées")
    print("🌟 Validation des tables fact/dimension")
    print("🔗 Validation des clés étrangères")
    print("🚫 AUCUNE dénormalisation à valider")
    print("=" * 60)
    
    validator = LoadValidator()
    
    try:
        # Valider la structure du schéma
        structure_ok = validator.validate_schema_structure()
        
        if not structure_ok:
            logger.error("❌ Validation structure échouée")
            return 1
        
        # Valider les tables critiques
        validator.validate_critical_tables()
        
        # Valider les tables fact/dimension
        validator.validate_fact_dimension_tables()
        
        # Valider les clés étrangères
        fk_ok = validator.validate_foreign_keys()
        
        # Valider la cohérence des données
        consistency_ok = validator.validate_data_consistency()
        
        # Générer le rapport final
        validation_success = validator.generate_validation_report()
        
        if validation_success:
            logger.info(f"\n🎉 VALIDATION LOAD TERMINÉE AVEC SUCCÈS!")
            logger.info(f"   🎯 Schéma load prêt pour la production")
            logger.info(f"   📊 {validator.stats['total_records']:,} enregistrements validés")
            logger.info(f"   🔗 {validator.stats['fk_relationships']} FK validées")
            return 0
        else:
            logger.warning(f"\n⚠️ VALIDATION LOAD AVEC AVERTISSEMENTS")
            return 1
    
    except Exception as e:
        logger.error(f"❌ Erreur fatale validation: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
