#!/usr/bin/env python3
"""
📊 CORRECT STAR SCHEMA INSERT - LOAD SCHEMA
Insertion des données dans le star schema correct

ARCHITECTURE CORRECTE:
1. 51 dimensions = COPIE EXACTE des données transform (sauf jiraissue)
2. fact_jiraissue = COPIE EXACTE de transform.jiraissue_clean + FK vers dimensions
3. 100% data integrity required

⚠️ EXACT DATA COPY FROM TRANSFORM TO LOAD
"""

import psycopg2
import logging
from datetime import datetime

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'correct_load_insert_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CorrectStarSchemaInserter:
    """
    Insertion CORRECTE des données dans le star schema:
    - 51 dimensions = exact copy from transform
    - fact_jiraissue = jiraissue data + FK to dimensions
    """

    def __init__(self):
        # Configuration Data Warehouse
        self.dw_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }

        # 51 tables dimension (TOUTES SAUF jiraissue)
        self.dimension_tables = [
            # CORE_BUSINESS (11 tables - sans jiraissue)
            'project', 'component', 'projectversion',
            'customfield', 'customfieldvalue', 'worklog', 'fileattachment',
            'issuelink', 'issuelinktype', 'label', 'nodeassociation',

            # USERS_GROUPS (8 tables)
            'cwd_user', 'cwd_group', 'cwd_membership', 'cwd_user_attributes',
            'userassociation', 'projectroleactor', 'app_user', 'cwd_directory',

            # WORKFLOWS (5 tables)
            'jiraworkflows', 'workflowscheme', 'workflowschemeentity',
            'os_currentstep', 'os_wfentry',

            # CONFIGURATION (6 tables)
            'fieldconfiguration', 'fieldconfigscheme', 'permissionscheme',
            'schemepermissions', 'fieldscreen', 'fieldscreentab',

            # CHANGES_HISTORY (3 tables)
            'changegroup', 'changeitem', 'jiraaction',

            # PLUGINS_MANAGEMENT (2 tables)
            'pluginversion', 'managedconfigurationitem',

            # LOOKUP_TABLES (6 tables)
            'issuestatus', 'priority', 'resolution', 'issuetype',
            'projectrole', 'projectcategory',

            # SCRIPT_RUNNER (4 tables)
            'AO_4B00E6_SR_USER_PROP', 'AO_4B00E6_STASH_SETTINGS',
            'AO_4B00E6_UPGRADE_BACKUP', 'AO_786AC3_SQL_FAVOURITE',

            # AGILE_BOARDS (3 tables)
            'AO_60DB71_RAPIDVIEW', 'AO_60DB71_SPRINT', 'AO_60DB71_ISSUERANKING',

            # JSM_AUDIT (3 tables)
            'AO_C77861_AUDIT_ENTITY', 'AO_C77861_AUDIT_ACTION_CACHE',
            'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]

        self.stats = {
            'start_time': datetime.now(),
            'dimensions_loaded': 0,
            'fact_loaded': 0,
            'total_records': 0,
            'errors': []
        }

    def connect_dw(self):
        """Connexion au Data Warehouse"""
        try:
            conn = psycopg2.connect(**self.dw_config)
            conn.autocommit = False
            return conn
        except Exception as e:
            logger.error(f"Erreur connexion DW: {e}")
            raise

    def insert_dimension_data(self, table_name):
        """Insérer les données dans une table dimension (EXACT COPY) - FIXED"""
        logger.info(f"Insertion dim_{table_name}")

        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()

        try:
            transform_table = f"{table_name}_clean"
            dim_table = f"dim_{table_name}"

            # Vérifier que les tables existent
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables
                WHERE table_schema = 'transform' AND table_name = %s
            """, (transform_table,))

            if cursor.fetchone()[0] == 0:
                logger.warning(f"   Table transform.{transform_table} manquante - SKIP")
                return 0

            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables
                WHERE table_schema = 'load' AND table_name = %s
            """, (dim_table,))

            if cursor.fetchone()[0] == 0:
                logger.warning(f"   Table load.{dim_table} manquante - SKIP")
                return 0

            # FIX 1: Vérifier si des données existent déjà pour éviter la duplication
            cursor.execute(f'SELECT COUNT(*) FROM load."{dim_table}"')
            existing_count = cursor.fetchone()[0]

            if existing_count > 0:
                logger.info(f"   Table {dim_table} déjà remplie ({existing_count} records) - SKIP")
                return existing_count

            # Obtenir les colonnes communes (EXACT MATCH)
            cursor.execute("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = 'transform' AND table_name = %s
                AND column_name IN (
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_schema = 'load' AND table_name = %s
                )
                ORDER BY ordinal_position
            """, (transform_table, dim_table))

            columns = [row[0] for row in cursor.fetchall()]

            if not columns:
                logger.warning(f"   Aucune colonne commune - SKIP")
                return 0

            # Construire la requête INSERT (EXACT COPY)
            columns_list = ', '.join([f'"{col}"' for col in columns])

            insert_sql = f'''
                INSERT INTO load."{dim_table}" ({columns_list})
                SELECT {columns_list}
                FROM transform."{transform_table}"
            '''

            # Exécuter l'insertion
            cursor.execute(insert_sql)
            records_inserted = cursor.rowcount

            dw_conn.commit()

            logger.info(f"   {records_inserted} enregistrements copiés")
            self.stats['total_records'] += records_inserted

            return records_inserted

        except Exception as e:
            logger.error(f"   Erreur insertion {table_name}: {e}")
            dw_conn.rollback()
            self.stats['errors'].append(f"{table_name}: {e}")
            return 0
        finally:
            dw_conn.close()

    def insert_all_dimension_data(self):
        """Insérer toutes les données dimension (51 tables)"""
        logger.info(f"\nINSERTION {len(self.dimension_tables)} DIMENSIONS")
        logger.info("=" * 70)

        success_count = 0
        for i, table_name in enumerate(self.dimension_tables, 1):
            logger.info(f"[{i:2d}/{len(self.dimension_tables)}] {table_name}")

            records = self.insert_dimension_data(table_name)
            if records > 0:
                success_count += 1

        self.stats['dimensions_loaded'] = success_count
        logger.info(f"\nDimensions chargées: {success_count}/{len(self.dimension_tables)}")
        return success_count

    def insert_fact_jiraissue_data(self):
        """Insérer les données fact_jiraissue (jiraissue data + FK NULL pour l'instant) - FIXED"""
        logger.info(f"\nINSERTION FACT_JIRAISSUE")
        logger.info("=" * 70)

        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()

        try:
            # FIX 1: Vérifier si des données existent déjà
            cursor.execute('SELECT COUNT(*) FROM load.fact_jiraissue')
            existing_count = cursor.fetchone()[0]

            if existing_count > 0:
                logger.info(f"   Table fact_jiraissue déjà remplie ({existing_count} records) - SKIP")
                self.stats['fact_loaded'] = existing_count
                return existing_count

            # FIX 2: Vérifier l'existence des tables avec gestion d'erreur améliorée
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables
                WHERE table_schema = 'transform' AND table_name = 'jiraissue_clean'
            """)

            if cursor.fetchone()[0] == 0:
                logger.error("Table transform.jiraissue_clean n'existe pas")
                return 0

            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables
                WHERE table_schema = 'load' AND table_name = 'fact_jiraissue'
            """)

            if cursor.fetchone()[0] == 0:
                logger.error("Table load.fact_jiraissue n'existe pas")
                return 0

            # FIX 3: Obtenir les colonnes communes avec validation (GARDER instance_id)
            cursor.execute("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = 'transform' AND table_name = 'jiraissue_clean'
                AND column_name IN (
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_schema = 'load' AND table_name = 'fact_jiraissue'
                    AND column_name NOT LIKE '%_id'  -- Exclure les colonnes FK
                    OR column_name = 'instance_id'   -- FIX: GARDER instance_id même si NULL
                )
                ORDER BY ordinal_position
            """)

            jiraissue_columns = [row[0] for row in cursor.fetchall()]

            if not jiraissue_columns:
                logger.error("Aucune colonne jiraissue commune trouvée")
                # FIX 4: Debug - montrer les colonnes disponibles
                cursor.execute("""
                    SELECT column_name FROM information_schema.columns
                    WHERE table_schema = 'transform' AND table_name = 'jiraissue_clean'
                    ORDER BY ordinal_position LIMIT 10
                """)
                transform_cols = [row[0] for row in cursor.fetchall()]
                logger.error(f"Colonnes transform disponibles: {transform_cols}")

                cursor.execute("""
                    SELECT column_name FROM information_schema.columns
                    WHERE table_schema = 'load' AND table_name = 'fact_jiraissue'
                    AND column_name NOT LIKE '%_id'
                    ORDER BY ordinal_position LIMIT 10
                """)
                fact_cols = [row[0] for row in cursor.fetchall()]
                logger.error(f"Colonnes fact disponibles: {fact_cols}")
                return 0

            # Construire la requête INSERT pour fact_jiraissue
            columns_list = ', '.join([f'"{col}"' for col in jiraissue_columns])

            insert_sql = f'''
                INSERT INTO load.fact_jiraissue ({columns_list})
                SELECT {columns_list}
                FROM transform.jiraissue_clean
            '''

            # FIX 5: Exécuter l'insertion avec validation
            logger.info(f"   Insertion de {len(jiraissue_columns)} colonnes: {jiraissue_columns[:5]}...")
            cursor.execute(insert_sql)
            records_inserted = cursor.rowcount

            dw_conn.commit()

            logger.info(f"   ✅ {records_inserted} enregistrements fact_jiraissue copiés")
            logger.info(f"   📊 Colonnes jiraissue: {len(jiraissue_columns)}")
            logger.info(f"   🔗 FK colonnes: NULL pour l'instant")

            self.stats['fact_loaded'] = records_inserted
            self.stats['total_records'] += records_inserted

            return records_inserted

        except Exception as e:
            logger.error(f"❌ Erreur insertion fact_jiraissue: {e}")
            dw_conn.rollback()
            self.stats['errors'].append(f"fact_jiraissue: {e}")
            return 0
        finally:
            dw_conn.close()

def main():
    """Point d'entrée principal"""
    print("INSERTION STAR SCHEMA CORRECT - LOAD")
    print("=" * 60)
    print("51 dimensions = exact copy from transform")
    print("fact_jiraissue = jiraissue data + FK")
    print("100% data integrity required")
    print("=" * 60)

    inserter = CorrectStarSchemaInserter()

    try:
        # Insérer toutes les dimensions
        dim_count = inserter.insert_all_dimension_data()

        # Insérer fact_jiraissue
        fact_count = inserter.insert_fact_jiraissue_data()

        # Statistiques finales
        duration = (datetime.now() - inserter.stats['start_time']).total_seconds()

        logger.info(f"\nINSERTION STAR SCHEMA TERMINÉE!")
        logger.info(f"Durée: {duration:.2f} secondes")
        logger.info(f"Dimensions chargées: {dim_count}/51")
        logger.info(f"Fact records: {fact_count:,}")
        logger.info(f"Total enregistrements: {inserter.stats['total_records']:,}")
        logger.info(f"Erreurs: {len(inserter.stats['errors'])}")

        if inserter.stats['errors']:
            logger.warning("Erreurs rencontrées:")
            for error in inserter.stats['errors'][:5]:
                logger.warning(f"   - {error}")

        if dim_count >= 45 and fact_count > 1000:
            logger.info(f"\nSTAR SCHEMA INSERTION RÉUSSIE!")
            logger.info(f"   {dim_count} dimensions chargées")
            logger.info(f"   {fact_count:,} fact records")
            logger.info(f"   Architecture star schema complète")
        else:
            logger.warning(f"\nINSERTION PARTIELLE")
            logger.warning(f"   Dimensions: {dim_count}/51")
            logger.warning(f"   Fact: {fact_count}")

    except Exception as e:
        logger.error(f"Erreur fatale: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())