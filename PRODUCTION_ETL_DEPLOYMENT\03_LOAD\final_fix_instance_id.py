#!/usr/bin/env python3
"""
🔧 FINAL FIX - INSTANCE_ID ISSUE
Correction finale du problème instance_id NULL

PROBLÈME IDENTIFIÉ:
- instance_id est NULL dans transform.jiraissue_clean
- instance_id est NOT NULL dans load.fact_jiraissue
- Insertion échoue à cause de la contrainte NOT NULL

SOLUTIONS:
1. Modifier la contrainte instance_id pour accepter NULL
2. OU insérer une valeur par défaut pour instance_id
3. OU exclure instance_id de l'insertion

OBJECTIF: Faire fonctionner fact_jiraissue à 100%
"""

import psycopg2
import logging
from datetime import datetime

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'final_fix_instance_id_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class InstanceIdFixer:
    """
    Correcteur final pour le problème instance_id
    """
    
    def __init__(self):
        # Configuration Data Warehouse
        self.dw_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }
    
    def connect_dw(self):
        """Connexion au Data Warehouse"""
        try:
            conn = psycopg2.connect(**self.dw_config)
            conn.autocommit = False
            return conn
        except Exception as e:
            logger.error(f"Erreur connexion DW: {e}")
            raise
    
    def analyze_instance_id_issue(self):
        """Analyser le problème instance_id"""
        logger.info("ANALYSE DU PROBLÈME INSTANCE_ID")
        logger.info("=" * 50)
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            # Vérifier les valeurs instance_id dans transform
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(instance_id) as non_null_instance_id,
                    COUNT(*) - COUNT(instance_id) as null_instance_id
                FROM transform.jiraissue_clean
            """)
            
            total, non_null, null_count = cursor.fetchone()
            
            logger.info(f"Transform.jiraissue_clean:")
            logger.info(f"  Total records: {total:,}")
            logger.info(f"  instance_id NOT NULL: {non_null:,}")
            logger.info(f"  instance_id NULL: {null_count:,}")
            
            # Vérifier la contrainte dans fact_jiraissue
            cursor.execute("""
                SELECT is_nullable 
                FROM information_schema.columns
                WHERE table_schema = 'load' 
                AND table_name = 'fact_jiraissue' 
                AND column_name = 'instance_id'
            """)
            
            is_nullable = cursor.fetchone()
            if is_nullable:
                logger.info(f"Fact_jiraissue.instance_id nullable: {is_nullable[0]}")
            
            return null_count > 0
            
        except Exception as e:
            logger.error(f"Erreur analyse: {e}")
            return False
        finally:
            conn.close()
    
    def fix_instance_id_constraint(self):
        """Modifier la contrainte instance_id pour accepter NULL"""
        logger.info("\nFIX 1: MODIFIER CONTRAINTE INSTANCE_ID")
        logger.info("-" * 50)
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            # Modifier la contrainte pour accepter NULL
            cursor.execute("""
                ALTER TABLE load.fact_jiraissue 
                ALTER COLUMN instance_id DROP NOT NULL
            """)
            
            conn.commit()
            logger.info("✅ Contrainte instance_id modifiée (NULL autorisé)")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur modification contrainte: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def insert_fact_jiraissue_with_null_instance_id(self):
        """Insérer fact_jiraissue avec instance_id NULL autorisé"""
        logger.info("\nFIX 2: INSERTION FACT_JIRAISSUE")
        logger.info("-" * 50)
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            # Vider fact_jiraissue d'abord
            cursor.execute("TRUNCATE TABLE load.fact_jiraissue")
            logger.info("✅ fact_jiraissue vidée")
            
            # Obtenir les colonnes communes (TOUTES les colonnes jiraissue)
            cursor.execute("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = 'transform' AND table_name = 'jiraissue_clean'
                AND column_name IN (
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_schema = 'load' AND table_name = 'fact_jiraissue'
                    AND column_name NOT LIKE '%_id' OR column_name = 'instance_id'
                )
                ORDER BY ordinal_position
            """)
            
            jiraissue_columns = [row[0] for row in cursor.fetchall()]
            
            if not jiraissue_columns:
                logger.error("❌ Aucune colonne commune trouvée")
                return 0
            
            # Insérer les données avec instance_id NULL autorisé
            columns_list = ', '.join([f'"{col}"' for col in jiraissue_columns])
            
            insert_sql = f'''
                INSERT INTO load.fact_jiraissue ({columns_list})
                SELECT {columns_list}
                FROM transform.jiraissue_clean
            '''
            
            logger.info(f"🚀 Insertion de {len(jiraissue_columns)} colonnes...")
            logger.info(f"📋 Colonnes: {jiraissue_columns[:5]}...")
            
            cursor.execute(insert_sql)
            records_inserted = cursor.rowcount
            
            conn.commit()
            
            logger.info(f"✅ {records_inserted:,} records fact_jiraissue insérés")
            return records_inserted
            
        except Exception as e:
            logger.error(f"❌ Erreur insertion: {e}")
            conn.rollback()
            return 0
        finally:
            conn.close()
    
    def validate_final_result(self):
        """Valider le résultat final"""
        logger.info("\nVALIDATION FINALE")
        logger.info("-" * 50)
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            # Compter fact_jiraissue
            cursor.execute('SELECT COUNT(*) FROM load.fact_jiraissue')
            fact_count = cursor.fetchone()[0]
            
            # Compter transform.jiraissue_clean
            cursor.execute('SELECT COUNT(*) FROM transform.jiraissue_clean')
            transform_count = cursor.fetchone()[0]
            
            # Compter les dimensions
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables
                WHERE table_schema = 'load' AND table_name LIKE 'dim_%'
            """)
            dim_tables_count = cursor.fetchone()[0]
            
            logger.info(f"📊 Transform jiraissue: {transform_count:,} records")
            logger.info(f"📊 Fact jiraissue: {fact_count:,} records")
            logger.info(f"📊 Tables dimension: {dim_tables_count}")
            
            success = fact_count == transform_count and fact_count > 0
            
            if success:
                logger.info(f"\n✅ SUCCÈS COMPLET - STAR SCHEMA OPÉRATIONNEL!")
                logger.info(f"🎯 fact_jiraissue: {fact_count:,} records")
                logger.info(f"🎯 Dimensions: {dim_tables_count} tables")
            else:
                logger.info(f"\n⚠️ PROBLÈME PERSISTANT")
                logger.info(f"❌ Expected: {transform_count:,}, Got: {fact_count:,}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Erreur validation: {e}")
            return False
        finally:
            conn.close()
    
    def run_final_fix(self):
        """Exécuter la correction finale complète"""
        logger.info("🔧 CORRECTION FINALE INSTANCE_ID")
        logger.info("=" * 60)
        
        # Étape 1: Analyser le problème
        has_null_instance_id = self.analyze_instance_id_issue()
        
        if not has_null_instance_id:
            logger.info("✅ Pas de problème instance_id détecté")
            return True
        
        # Étape 2: Modifier la contrainte
        if not self.fix_instance_id_constraint():
            logger.error("❌ Échec modification contrainte")
            return False
        
        # Étape 3: Insérer les données
        records_inserted = self.insert_fact_jiraissue_with_null_instance_id()
        
        if records_inserted == 0:
            logger.error("❌ Échec insertion fact_jiraissue")
            return False
        
        # Étape 4: Valider le résultat
        return self.validate_final_result()

def main():
    """Point d'entrée principal"""
    print("🔧 CORRECTION FINALE - PROBLÈME INSTANCE_ID")
    print("=" * 60)
    print("🔍 Analyse du problème instance_id NULL")
    print("🔧 Modification contrainte NOT NULL")
    print("📊 Insertion fact_jiraissue")
    print("✅ Validation finale")
    print("=" * 60)
    
    fixer = InstanceIdFixer()
    
    try:
        success = fixer.run_final_fix()
        return 0 if success else 1
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
