#!/usr/bin/env python3
"""
🔍 DEBUG FACT_JIRAISSUE INSERTION
Analyse détaillée du problème d'insertion fact_jiraissue

OBJECTIF: Identifier EXACTEMENT pourquoi l'insertion échoue
"""

import psycopg2
import logging
from datetime import datetime

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'debug_fact_insertion_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FactInsertionDebugger:
    """
    Débogueur pour l'insertion fact_jiraissue
    """
    
    def __init__(self):
        # Configuration Data Warehouse
        self.dw_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }
    
    def connect_dw(self):
        """Connexion au Data Warehouse"""
        try:
            conn = psycopg2.connect(**self.dw_config)
            conn.autocommit = False
            return conn
        except Exception as e:
            logger.error(f"Erreur connexion DW: {e}")
            raise
    
    def analyze_column_mapping(self):
        """Analyser le mapping des colonnes entre transform et fact"""
        logger.info("ANALYSE MAPPING COLONNES")
        logger.info("=" * 50)
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            # Colonnes transform
            cursor.execute("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_schema = 'transform' AND table_name = 'jiraissue_clean'
                ORDER BY ordinal_position
            """)
            
            transform_cols = {row[0]: (row[1], row[2]) for row in cursor.fetchall()}
            
            # Colonnes fact (non-FK)
            cursor.execute("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_schema = 'load' AND table_name = 'fact_jiraissue'
                AND column_name NOT LIKE '%_id'
                ORDER BY ordinal_position
            """)
            
            fact_cols = {row[0]: (row[1], row[2]) for row in cursor.fetchall()}
            
            logger.info(f"Transform colonnes: {len(transform_cols)}")
            logger.info(f"Fact colonnes (non-FK): {len(fact_cols)}")
            
            # Colonnes communes
            common_cols = []
            for col in transform_cols:
                if col in fact_cols:
                    common_cols.append(col)
                    t_type, t_null = transform_cols[col]
                    f_type, f_null = fact_cols[col]
                    
                    if t_type != f_type or t_null != f_null:
                        logger.warning(f"⚠️ {col}: transform({t_type},{t_null}) vs fact({f_type},{f_null})")
            
            logger.info(f"Colonnes communes: {len(common_cols)}")
            logger.info(f"Premières 10: {common_cols[:10]}")
            
            return common_cols
            
        except Exception as e:
            logger.error(f"Erreur analyse mapping: {e}")
            return []
        finally:
            conn.close()
    
    def test_simple_insertion(self, columns):
        """Tester une insertion simple avec les colonnes communes"""
        logger.info("\nTEST INSERTION SIMPLE")
        logger.info("-" * 50)
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            # Vider fact_jiraissue
            cursor.execute("TRUNCATE TABLE load.fact_jiraissue")
            logger.info("✅ fact_jiraissue vidée")
            
            # Tester avec seulement les 5 premières colonnes
            test_columns = columns[:5]
            columns_list = ', '.join([f'"{col}"' for col in test_columns])
            
            insert_sql = f'''
                INSERT INTO load.fact_jiraissue ({columns_list})
                SELECT {columns_list}
                FROM transform.jiraissue_clean
                LIMIT 1
            '''
            
            logger.info(f"🧪 Test avec {len(test_columns)} colonnes: {test_columns}")
            logger.info(f"📋 SQL: {insert_sql}")
            
            cursor.execute(insert_sql)
            records_inserted = cursor.rowcount
            
            conn.commit()
            
            logger.info(f"✅ Test réussi: {records_inserted} record inséré")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test échoué: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def test_progressive_insertion(self, columns):
        """Tester l'insertion progressive colonne par colonne"""
        logger.info("\nTEST INSERTION PROGRESSIVE")
        logger.info("-" * 50)
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        working_columns = []
        
        for i, col in enumerate(columns, 1):
            try:
                # Vider fact_jiraissue
                cursor.execute("TRUNCATE TABLE load.fact_jiraissue")
                
                # Tester avec les colonnes jusqu'à maintenant
                test_columns = columns[:i]
                columns_list = ', '.join([f'"{col}"' for col in test_columns])
                
                insert_sql = f'''
                    INSERT INTO load.fact_jiraissue ({columns_list})
                    SELECT {columns_list}
                    FROM transform.jiraissue_clean
                    LIMIT 1
                '''
                
                cursor.execute(insert_sql)
                conn.commit()
                
                working_columns = test_columns.copy()
                logger.info(f"✅ [{i:2d}/{len(columns)}] {col} - OK")
                
            except Exception as e:
                logger.error(f"❌ [{i:2d}/{len(columns)}] {col} - ÉCHEC: {e}")
                conn.rollback()
                break
        
        logger.info(f"\n📊 Colonnes fonctionnelles: {len(working_columns)}/{len(columns)}")
        logger.info(f"📋 Dernières colonnes OK: {working_columns[-5:] if len(working_columns) >= 5 else working_columns}")
        
        conn.close()
        return working_columns
    
    def insert_with_working_columns(self, working_columns):
        """Insérer avec seulement les colonnes fonctionnelles"""
        logger.info("\nINSERTION AVEC COLONNES FONCTIONNELLES")
        logger.info("-" * 50)
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            # Vider fact_jiraissue
            cursor.execute("TRUNCATE TABLE load.fact_jiraissue")
            logger.info("✅ fact_jiraissue vidée")
            
            # Insérer avec toutes les colonnes fonctionnelles
            columns_list = ', '.join([f'"{col}"' for col in working_columns])
            
            insert_sql = f'''
                INSERT INTO load.fact_jiraissue ({columns_list})
                SELECT {columns_list}
                FROM transform.jiraissue_clean
            '''
            
            logger.info(f"🚀 Insertion avec {len(working_columns)} colonnes...")
            
            cursor.execute(insert_sql)
            records_inserted = cursor.rowcount
            
            conn.commit()
            
            logger.info(f"✅ {records_inserted:,} records fact_jiraissue insérés")
            return records_inserted
            
        except Exception as e:
            logger.error(f"❌ Erreur insertion finale: {e}")
            conn.rollback()
            return 0
        finally:
            conn.close()
    
    def run_debug_analysis(self):
        """Exécuter l'analyse de débogage complète"""
        logger.info("🔍 DÉBOGAGE INSERTION FACT_JIRAISSUE")
        logger.info("=" * 60)
        
        # Étape 1: Analyser le mapping des colonnes
        common_columns = self.analyze_column_mapping()
        
        if not common_columns:
            logger.error("❌ Aucune colonne commune trouvée")
            return False
        
        # Étape 2: Test simple
        if not self.test_simple_insertion(common_columns):
            logger.error("❌ Test simple échoué")
            return False
        
        # Étape 3: Test progressif pour identifier la colonne problématique
        working_columns = self.test_progressive_insertion(common_columns)
        
        if not working_columns:
            logger.error("❌ Aucune colonne fonctionnelle")
            return False
        
        # Étape 4: Insertion finale avec colonnes fonctionnelles
        records_inserted = self.insert_with_working_columns(working_columns)
        
        success = records_inserted > 0
        
        if success:
            logger.info(f"\n✅ DÉBOGAGE RÉUSSI")
            logger.info(f"🎯 {records_inserted:,} records insérés")
            logger.info(f"📊 {len(working_columns)}/{len(common_columns)} colonnes utilisées")
        else:
            logger.info(f"\n❌ DÉBOGAGE ÉCHOUÉ")
        
        return success

def main():
    """Point d'entrée principal"""
    print("🔍 DÉBOGAGE INSERTION FACT_JIRAISSUE")
    print("=" * 60)
    print("📊 Analyse mapping colonnes")
    print("🧪 Test insertion simple")
    print("🔄 Test insertion progressive")
    print("✅ Insertion finale")
    print("=" * 60)
    
    debugger = FactInsertionDebugger()
    
    try:
        success = debugger.run_debug_analysis()
        return 0 if success else 1
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
