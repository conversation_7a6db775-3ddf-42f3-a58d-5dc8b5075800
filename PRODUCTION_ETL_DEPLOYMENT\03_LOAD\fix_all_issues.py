#!/usr/bin/env python3
"""
🔧 FIX ALL ISSUES - COMPREHENSIVE REPAIR
Correction complète de tous les problèmes identifiés dans l'analyse

CORRECTIONS APPLIQUÉES:
1. ✅ Fix fact_jiraissue insertion error
2. ✅ Fix data duplication in dimensions
3. ✅ Fix structure mismatches
4. ✅ Clear existing data before re-insertion
5. ✅ Validate all operations

OBJECTIF: 100% success rate pour le star schema
"""

import psycopg2
import logging
from datetime import datetime

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'fix_all_issues_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComprehensiveFixer:
    """
    Correcteur complet pour tous les problèmes ETL
    """
    
    def __init__(self):
        # Configuration Data Warehouse
        self.dw_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        # 51 tables dimension
        self.dimension_tables = [
            # CORE_BUSINESS (11 tables - sans jiraissue)
            'project', 'component', 'projectversion',
            'customfield', 'customfieldvalue', 'worklog', 'fileattachment',
            'issuelink', 'issuelinktype', 'label', 'nodeassociation',
            
            # USERS_GROUPS (8 tables)
            'cwd_user', 'cwd_group', 'cwd_membership', 'cwd_user_attributes',
            'userassociation', 'projectroleactor', 'app_user', 'cwd_directory',
            
            # WORKFLOWS (5 tables)
            'jiraworkflows', 'workflowscheme', 'workflowschemeentity',
            'os_currentstep', 'os_wfentry',
            
            # CONFIGURATION (6 tables)
            'fieldconfiguration', 'fieldconfigscheme', 'permissionscheme',
            'schemepermissions', 'fieldscreen', 'fieldscreentab',
            
            # CHANGES_HISTORY (3 tables)
            'changegroup', 'changeitem', 'jiraaction',
            
            # PLUGINS_MANAGEMENT (2 tables)
            'pluginversion', 'managedconfigurationitem',
            
            # LOOKUP_TABLES (6 tables)
            'issuestatus', 'priority', 'resolution', 'issuetype',
            'projectrole', 'projectcategory',
            
            # SCRIPT_RUNNER (4 tables)
            'AO_4B00E6_SR_USER_PROP', 'AO_4B00E6_STASH_SETTINGS',
            'AO_4B00E6_UPGRADE_BACKUP', 'AO_786AC3_SQL_FAVOURITE',
            
            # AGILE_BOARDS (3 tables)
            'AO_60DB71_RAPIDVIEW', 'AO_60DB71_SPRINT', 'AO_60DB71_ISSUERANKING',
            
            # JSM_AUDIT (3 tables)
            'AO_C77861_AUDIT_ENTITY', 'AO_C77861_AUDIT_ACTION_CACHE',
            'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]
        
        self.stats = {
            'start_time': datetime.now(),
            'tables_fixed': 0,
            'records_processed': 0,
            'errors_fixed': 0,
            'issues': []
        }
    
    def connect_dw(self):
        """Connexion au Data Warehouse"""
        try:
            conn = psycopg2.connect(**self.dw_config)
            conn.autocommit = False
            return conn
        except Exception as e:
            logger.error(f"❌ Erreur connexion DW: {e}")
            raise
    
    def clear_all_load_data(self):
        """Vider toutes les données du schéma load pour éviter la duplication"""
        logger.info("🧹 NETTOYAGE COMPLET DU SCHÉMA LOAD")
        logger.info("=" * 60)
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            # Désactiver les contraintes FK temporairement
            cursor.execute("SET session_replication_role = replica;")
            
            # Vider fact_jiraissue
            cursor.execute("TRUNCATE TABLE load.fact_jiraissue CASCADE")
            logger.info("✅ fact_jiraissue vidée")
            
            # Vider toutes les dimensions
            cleared_count = 0
            for table_name in self.dimension_tables:
                dim_table = f"dim_{table_name}"
                try:
                    cursor.execute(f'TRUNCATE TABLE load."{dim_table}" CASCADE')
                    cleared_count += 1
                    logger.info(f"   ✅ {dim_table} vidée")
                except Exception as e:
                    logger.warning(f"   ⚠️ {dim_table}: {e}")
            
            # Réactiver les contraintes FK
            cursor.execute("SET session_replication_role = DEFAULT;")
            
            conn.commit()
            logger.info(f"🧹 Nettoyage terminé: {cleared_count} tables vidées")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur nettoyage: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def fix_dimension_insertion(self, table_name):
        """Corriger l'insertion d'une dimension"""
        logger.info(f"🔧 Fix dimension: {table_name}")
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            transform_table = f"{table_name}_clean"
            dim_table = f"dim_{table_name}"
            
            # Vérifier l'existence des tables
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables
                WHERE table_schema = 'transform' AND table_name = %s
            """, (transform_table,))
            
            if cursor.fetchone()[0] == 0:
                logger.warning(f"   ⚠️ Table transform.{transform_table} manquante")
                return 0
            
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables
                WHERE table_schema = 'load' AND table_name = %s
            """, (dim_table,))
            
            if cursor.fetchone()[0] == 0:
                logger.warning(f"   ⚠️ Table load.{dim_table} manquante")
                return 0
            
            # Obtenir les colonnes communes
            cursor.execute("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = 'transform' AND table_name = %s
                AND column_name IN (
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_schema = 'load' AND table_name = %s
                )
                ORDER BY ordinal_position
            """, (transform_table, dim_table))
            
            columns = [row[0] for row in cursor.fetchall()]
            
            if not columns:
                logger.warning(f"   ⚠️ Aucune colonne commune")
                return 0
            
            # Insérer les données
            columns_list = ', '.join([f'"{col}"' for col in columns])
            
            insert_sql = f'''
                INSERT INTO load."{dim_table}" ({columns_list})
                SELECT {columns_list}
                FROM transform."{transform_table}"
            '''
            
            cursor.execute(insert_sql)
            records_inserted = cursor.rowcount
            
            conn.commit()
            
            logger.info(f"   ✅ {records_inserted} records insérés")
            self.stats['records_processed'] += records_inserted
            
            return records_inserted
            
        except Exception as e:
            logger.error(f"   ❌ Erreur: {e}")
            conn.rollback()
            self.stats['issues'].append(f"{table_name}: {e}")
            return 0
        finally:
            conn.close()
    
    def fix_fact_jiraissue_insertion(self):
        """Corriger l'insertion de fact_jiraissue"""
        logger.info("🔧 FIX FACT_JIRAISSUE INSERTION")
        logger.info("=" * 60)
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            # Vérifier l'existence des tables
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables
                WHERE table_schema = 'transform' AND table_name = 'jiraissue_clean'
            """)
            
            if cursor.fetchone()[0] == 0:
                logger.error("❌ Table transform.jiraissue_clean manquante")
                return 0
            
            # Obtenir les colonnes communes (exclure les FK)
            cursor.execute("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = 'transform' AND table_name = 'jiraissue_clean'
                AND column_name IN (
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_schema = 'load' AND table_name = 'fact_jiraissue'
                    AND column_name NOT LIKE '%_id'
                )
                ORDER BY ordinal_position
            """)
            
            jiraissue_columns = [row[0] for row in cursor.fetchall()]
            
            if not jiraissue_columns:
                logger.error("❌ Aucune colonne commune trouvée")
                return 0
            
            # Insérer les données
            columns_list = ', '.join([f'"{col}"' for col in jiraissue_columns])
            
            insert_sql = f'''
                INSERT INTO load.fact_jiraissue ({columns_list})
                SELECT {columns_list}
                FROM transform.jiraissue_clean
            '''
            
            logger.info(f"🚀 Insertion de {len(jiraissue_columns)} colonnes...")
            cursor.execute(insert_sql)
            records_inserted = cursor.rowcount
            
            conn.commit()
            
            logger.info(f"✅ {records_inserted} records fact_jiraissue insérés")
            self.stats['records_processed'] += records_inserted
            
            return records_inserted
            
        except Exception as e:
            logger.error(f"❌ Erreur fact_jiraissue: {e}")
            conn.rollback()
            self.stats['issues'].append(f"fact_jiraissue: {e}")
            return 0
        finally:
            conn.close()

    def run_comprehensive_fix(self):
        """Exécuter la correction complète"""
        logger.info("🚀 DÉMARRAGE CORRECTION COMPLÈTE")
        logger.info("=" * 80)

        # ÉTAPE 1: Nettoyage complet
        if not self.clear_all_load_data():
            logger.error("❌ Échec du nettoyage - ARRÊT")
            return False

        # ÉTAPE 2: Correction des dimensions
        logger.info(f"\n📊 CORRECTION DES DIMENSIONS ({len(self.dimension_tables)} tables)")
        logger.info("-" * 60)

        dimensions_fixed = 0
        for i, table_name in enumerate(self.dimension_tables, 1):
            logger.info(f"[{i:2d}/51] {table_name}")
            records = self.fix_dimension_insertion(table_name)
            if records > 0:
                dimensions_fixed += 1
                self.stats['tables_fixed'] += 1

        # ÉTAPE 3: Correction de fact_jiraissue
        logger.info(f"\n🎯 CORRECTION FACT_JIRAISSUE")
        logger.info("-" * 60)

        fact_records = self.fix_fact_jiraissue_insertion()
        if fact_records > 0:
            self.stats['tables_fixed'] += 1

        # ÉTAPE 4: Validation finale
        self.validate_final_state()

        # ÉTAPE 5: Rapport final
        return self.generate_fix_report()

    def validate_final_state(self):
        """Valider l'état final après corrections"""
        logger.info(f"\n✅ VALIDATION FINALE")
        logger.info("-" * 60)

        conn = self.connect_dw()
        cursor = conn.cursor()

        try:
            # Valider fact_jiraissue
            cursor.execute('SELECT COUNT(*) FROM load.fact_jiraissue')
            fact_count = cursor.fetchone()[0]
            logger.info(f"📊 fact_jiraissue: {fact_count:,} records")

            # Valider dimensions
            total_dim_records = 0
            populated_dims = 0

            for table_name in self.dimension_tables:
                dim_table = f"dim_{table_name}"
                try:
                    cursor.execute(f'SELECT COUNT(*) FROM load."{dim_table}"')
                    count = cursor.fetchone()[0]
                    total_dim_records += count
                    if count > 0:
                        populated_dims += 1
                except:
                    pass

            logger.info(f"📊 Dimensions: {populated_dims}/51 tables remplies")
            logger.info(f"📊 Total records dimensions: {total_dim_records:,}")
            logger.info(f"📊 Total records load: {fact_count + total_dim_records:,}")

            # Valider FK
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.table_constraints
                WHERE table_schema = 'load' AND constraint_type = 'FOREIGN KEY'
            """)
            fk_count = cursor.fetchone()[0]
            logger.info(f"🔗 Foreign keys: {fk_count}")

        except Exception as e:
            logger.error(f"❌ Erreur validation: {e}")
        finally:
            conn.close()

    def generate_fix_report(self):
        """Générer le rapport de correction"""
        duration = (datetime.now() - self.stats['start_time']).total_seconds()

        logger.info(f"\n📋 RAPPORT DE CORRECTION")
        logger.info("=" * 80)
        logger.info(f"⏱️ Durée: {duration:.2f} secondes")
        logger.info(f"🔧 Tables corrigées: {self.stats['tables_fixed']}")
        logger.info(f"📊 Records traités: {self.stats['records_processed']:,}")
        logger.info(f"❌ Problèmes: {len(self.stats['issues'])}")

        if self.stats['issues']:
            logger.info(f"\n⚠️ PROBLÈMES RESTANTS:")
            for issue in self.stats['issues'][:10]:
                logger.info(f"   - {issue}")

        success = len(self.stats['issues']) == 0 and self.stats['tables_fixed'] > 0

        if success:
            logger.info(f"\n✅ CORRECTION RÉUSSIE - STAR SCHEMA OPÉRATIONNEL!")
        else:
            logger.info(f"\n⚠️ CORRECTION PARTIELLE - VÉRIFICATION NÉCESSAIRE")

        return success

def main():
    """Point d'entrée principal"""
    print("🔧 CORRECTION COMPLÈTE DE TOUS LES PROBLÈMES")
    print("=" * 60)
    print("🧹 Nettoyage des données dupliquées")
    print("🔧 Correction des insertions")
    print("✅ Validation finale")
    print("=" * 60)

    fixer = ComprehensiveFixer()

    try:
        success = fixer.run_comprehensive_fix()
        return 0 if success else 1
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
